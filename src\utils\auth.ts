import { store } from '@/store'
import { loginEvent, SHOW_LOGIN_EVENT } from '@/api/request'

// Token 相关操作
export const getToken = (): string | null => {
  return localStorage.getItem('token')
}

export const setToken = (token: string): void => {
  localStorage.setItem('token', token)
}

export const getRefreshToken = (): string | null => {
  return localStorage.getItem('refresh_token')
}

export const setRefreshToken = (refreshToken: string): void => {
  localStorage.setItem('refresh_token', refreshToken)
}

export const removeToken = (): void => {
  localStorage.removeItem('token')
  localStorage.removeItem('refresh_token')
  localStorage.removeItem("isNew")
  localStorage.removeItem('isLogin')
  removeUserInfo()
}
export const removePddActivityData = (): void => {
  sessionStorage.removeItem('pddActivityData')
}

// 用户信息相关操作
export const getUserInfo = () => {
  const userInfo = localStorage.getItem('userInfo')
  return userInfo ? JSON.parse(userInfo) : null
}

export const setUserInfo = (userInfo: object) => {
  localStorage.setItem('userInfo', JSON.stringify(userInfo))
}

export const removeUserInfo = () => {
  localStorage.removeItem('userInfo')
}

// 登录状态检查
export const isAuthenticated = (): boolean => {
  return !!getToken()
}

// 存储目标路由
let targetRoute: string | null = null;

// 检查是否需要登录
export const checkAuth = (to: any, from: any) => {
  console.log('checkAuth', isAuthenticated(),to,from)
  if (to.meta?.requiresAuth && !isAuthenticated()) {
    console.log('checkAuth', isAuthenticated())
    // 存储目标路由
    targetRoute = to.fullPath;
    // 立即触发显示登录弹窗事件
    loginEvent.dispatchEvent(new Event(SHOW_LOGIN_EVENT));
    return '/home';
  }
  return true;
}

// 获取并清除目标路由
export const getAndClearTargetRoute = () => {
  const route = targetRoute;
  targetRoute = null;
  return route;
}

// 登出操作
export const logout = () => {
  removeToken()
  removeUserInfo()
  store.commit('auth/CLEAR_AUTH')
  window.location.href = '/home'
} 