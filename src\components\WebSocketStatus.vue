<template>
  <div
    v-if="showStatus"
    class="websocket-status"
    :class="[statusClass, { 'long-press': isLongPress }]"
    :style="{
      left: position.x + 'px',
      top: position.y + 'px',
      transform: isDragging ? 'scale(1.02)' : 'scale(1)',
    }"
  >
    <div
      class="status-header"
      @mousedown="startDrag"
      @touchstart="startTouchDrag"
      @dblclick="resetPosition"
      :class="{ dragging: isDragging }"
      :title="
        isTouchDevice
          ? '长按拖拽移动位置，双击重置到右上角'
          : '拖拽移动位置，双击重置到右上角'
      "
    >
      <span class="drag-handle" @click="showStatus = false">⋮⋮</span>
      <span class="status-indicator" :class="indicatorClass"></span>
      <span class="status-text">{{ statusText }}</span>
      <span class="toggle-icon" @click="toggleExpanded">{{
        expanded ? "▼" : "▶"
      }}</span>
    </div>

    <div v-if="expanded" class="status-details">
      <div class="detail-row">
        <span class="label">连接状态:</span>
        <span class="value" :class="wsStatus.isConnected ? 'success' : 'error'">
          {{ wsStatus.isConnected ? "已连接" : "未连接" }}
        </span>
      </div>

      <div class="detail-row">
        <span class="label">WebSocket状态:</span>
        <span class="value">{{ getWebSocketStateText(wsStatus.state) }}</span>
      </div>

      <div class="detail-row">
        <span class="label">心跳状态:</span>
        <span
          class="value"
          :class="heartbeatStatus.isHealthy ? 'success' : 'error'"
        >
          {{ heartbeatStatus.isRunning ? "运行中" : "已停止" }}
          {{ heartbeatStatus.isHealthy ? "(健康)" : "(异常)" }}
        </span>
      </div>

      <div class="detail-row">
        <span class="label">网络状态:</span>
        <span
          class="value"
          :class="getNetworkStatusClass(networkStatus.networkStatus)"
        >
          {{ getNetworkStatusText(networkStatus.networkStatus) }}
        </span>
      </div>

      <div class="detail-row">
        <span class="label">Token状态:</span>
        <span class="value" :class="hasValidToken ? 'success' : 'error'">
          {{ hasValidToken ? "有效" : "无效" }}
        </span>
      </div>

      <div class="detail-row">
        <span class="label">用户信息:</span>
        <span class="value" :class="hasUserInfo ? 'success' : 'error'">
          {{ hasUserInfo ? "已加载" : "未加载" }}
        </span>
      </div>

      <div class="detail-row">
        <span class="label">保持器状态:</span>
        <span
          class="value"
          :class="keeperStatus.isKeeping ? 'success' : 'warning'"
        >
          {{ keeperStatus.isKeeping ? "运行中" : "已停止" }}
        </span>
      </div>

      <div class="detail-row">
        <span class="label">1006处理:</span>
        <span class="value success">已忽略</span>
      </div>

      <div class="actions">
        <button @click="manualCheck" class="action-btn">手动检查</button>
        <button @click="forceReconnect" class="action-btn">强制重连</button>
        <button @click="forceCloseconnect" class="action-btn">强制断开</button>
        <button @click="toggleKeeper" class="action-btn">
          {{ keeperStatus.isKeeping ? "停止保持器" : "启动保持器" }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useStore } from "vuex";
import websocketService from "@/utils/websocket";
import networkMonitor, { NetworkStatus } from "@/utils/networkMonitor";
import websocketKeeper from "@/utils/websocketKeeper";
import { getToken } from "@/utils/auth";

// 响应式状态
const expanded = ref(false);
const wsStatus = ref({
  isConnected: false,
  state: 0,
});
const heartbeatStatus = ref({
  isRunning: false,
  isHealthy: false,
});
const networkStatus = ref({
  networkStatus: NetworkStatus.ONLINE,
});
const keeperStatus = ref({
  isKeeping: false,
});

// 拖拽相关状态
const isDragging = ref(false);
const position = ref({
  x: 10,
  y: 10,
});
const dragOffset = ref({
  x: 0,
  y: 0,
});

// 移动端相关状态
const isTouchDevice = ref(false);
const touchStartTime = ref(0);
const lastTouchTime = ref(0);
const longPressTimer = ref<NodeJS.Timeout | null>(null);
const isLongPress = ref(false);

const store = useStore();

// 计算属性
const showStatus = computed(() => {
  // 只在开发环境或特定条件下显示
  return ;
});

const hasValidToken = computed(() => {
  return !!getToken();
});

const hasUserInfo = computed(() => {
  return !!store.state.auth.user?.uuid;
});

const statusClass = computed(() => {
  if (!wsStatus.value.isConnected) return "status-error";
  if (!heartbeatStatus.value.isHealthy) return "status-warning";
  return "status-success";
});

const indicatorClass = computed(() => {
  if (!wsStatus.value.isConnected) return "indicator-error";
  if (!heartbeatStatus.value.isHealthy) return "indicator-warning";
  return "indicator-success";
});

const statusText = computed(() => {
  if (!wsStatus.value.isConnected) return "WebSocket 未连接";
  if (!heartbeatStatus.value.isHealthy) return "WebSocket 心跳异常";
  return "WebSocket 正常";
});

// 方法
const toggleExpanded = () => {
  console.log(expanded.value);
  expanded.value = !expanded.value;
};

const updateStatus = () => {
  wsStatus.value = {
    isConnected: websocketService.getIsConnected(),
    state: websocketService.getState(),
  };

  heartbeatStatus.value = websocketService.getHeartbeatStatus();

  const networkCurrentStatus = networkMonitor.getCurrentStatus();
  networkStatus.value = {
    networkStatus: networkCurrentStatus.networkStatus,
  };

  keeperStatus.value = websocketKeeper.getStatus();
};

const getWebSocketStateText = (state: number): string => {
  const states = {
    0: "连接中",
    1: "已连接",
    2: "关闭中",
    3: "已关闭",
  };
  return states[state as keyof typeof states] || "未知";
};

const getNetworkStatusText = (status: NetworkStatus): string => {
  const texts = {
    [NetworkStatus.ONLINE]: "在线",
    [NetworkStatus.OFFLINE]: "离线",
    [NetworkStatus.SLOW]: "缓慢",
    [NetworkStatus.UNKNOWN]: "未知",
  };
  return texts[status] || "未知";
};

const getNetworkStatusClass = (status: NetworkStatus): string => {
  const classes = {
    [NetworkStatus.ONLINE]: "success",
    [NetworkStatus.OFFLINE]: "error",
    [NetworkStatus.SLOW]: "warning",
    [NetworkStatus.UNKNOWN]: "warning",
  };
  return classes[status] || "warning";
};

const manualCheck = async () => {
  console.log("手动触发WebSocket检查");
  await networkMonitor.manualWebSocketCheck();
  await websocketKeeper.manualCheck();
  updateStatus();
};

const forceReconnect = async () => {
  console.log("强制重连WebSocket");
  try {
    await websocketService.forceReconnect();
    await websocketKeeper.start();
    updateStatus();
  } catch (error) {
    console.error("强制重连失败:", error);
  }
};
const forceCloseconnect = async () => {
  console.log("强制关闭WebSocket");
  try {
    await websocketService.disconnect();
    await websocketKeeper.stop();
    updateStatus();
  } catch (error) {
    console.error("强制关闭失败:", error);
  }
};
const toggleKeeper = () => {
  if (keeperStatus.value.isKeeping) {
    websocketKeeper.stop();
  } else {
    websocketKeeper.start();
  }
  updateStatus();
};

// 拖拽相关方法
const startDrag = (event: MouseEvent) => {
  // 只有左键点击才开始拖拽
  if (event.button !== 0) return;

  // 阻止点击事件冒泡，避免触发展开/收起
  // event.stopPropagation();
  // event.preventDefault();

  isDragging.value = true;

  // 记录鼠标相对于组件的偏移
  const rect = (event.target as HTMLElement)
    .closest(".websocket-status")
    ?.getBoundingClientRect();
  if (rect) {
    dragOffset.value = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
    };
  }

  // 添加全局事件监听
  document.addEventListener("mousemove", onDrag);
  document.addEventListener("mouseup", stopDrag);

  // 防止文本选择和页面滚动
  document.body.style.userSelect = "none";
  document.body.style.overflow = "hidden";
};

const onDrag = (event: MouseEvent) => {
  if (!isDragging.value) return;

  // 计算新位置
  const newX = event.clientX - dragOffset.value.x;
  const newY = event.clientY - dragOffset.value.y;

  // 限制在视窗范围内
  const maxX = window.innerWidth - 250; // 组件宽度约250px
  const maxY = window.innerHeight - 100; // 预留底部空间

  position.value = {
    x: Math.max(0, Math.min(newX, maxX)),
    y: Math.max(0, Math.min(newY, maxY)),
  };
};

const stopDrag = () => {
  isDragging.value = false;

  // 移除全局事件监听
  document.removeEventListener("mousemove", onDrag);
  document.removeEventListener("mouseup", stopDrag);

  // 恢复文本选择和页面滚动
  document.body.style.userSelect = "";
  document.body.style.overflow = "";

  // 保存位置到localStorage
  localStorage.setItem(
    "websocketStatusPosition",
    JSON.stringify(position.value)
  );
};

// 从localStorage恢复位置
const restorePosition = () => {
  const savedPosition = localStorage.getItem("websocketStatusPosition");
  if (savedPosition) {
    try {
      const parsed = JSON.parse(savedPosition);
      // 验证位置是否在有效范围内
      if (
        parsed.x >= 0 &&
        parsed.y >= 0 &&
        parsed.x <= window.innerWidth - 250 &&
        parsed.y <= window.innerHeight - 100
      ) {
        position.value = parsed;
      }
    } catch (error) {
      console.warn("恢复WebSocket状态位置失败:", error);
    }
  }
};

// 重置位置到右上角
const resetPosition = (event: MouseEvent | TouchEvent) => {
  event.stopPropagation();
  position.value = {
    x: window.innerWidth - 260, // 右上角，留出一些边距
    y: 10,
  };
  // 保存新位置
  localStorage.setItem(
    "websocketStatusPosition",
    JSON.stringify(position.value)
  );
};

// 触摸拖拽开始
const startTouchDrag = (event: TouchEvent) => {
  // 记录触摸开始时间，用于区分点击和拖拽
  touchStartTime.value = Date.now();
  isLongPress.value = false;

  // 阻止默认行为（如滚动、缩放）
  // event.preventDefault();
  // event.stopPropagation();

  const touch = event.touches[0];
  if (!touch) return;

  isTouchDevice.value = true;

  // 记录触摸相对于组件的偏移
  const rect = (event.target as HTMLElement)
    .closest(".websocket-status")
    ?.getBoundingClientRect();
  if (rect) {
    dragOffset.value = {
      x: touch.clientX - rect.left,
      y: touch.clientY - rect.top,
    };
  }

  // 设置长按检测定时器
  longPressTimer.value = setTimeout(() => {
    isLongPress.value = true;
    isDragging.value = true;

    // 添加触摸事件监听
    document.addEventListener("touchmove", onTouchDrag, { passive: false });
    document.addEventListener("touchend", stopTouchDrag);

    // 防止页面滚动
    document.body.style.overflow = "hidden";
    document.body.style.position = "fixed";
    document.body.style.width = "100%";

    // 触觉反馈（如果支持）
    if (navigator.vibrate) {
      navigator.vibrate(50);
    }
  }, 300); // 300ms后认为是长按

  // 临时添加touchend监听，用于取消长按检测
  const cancelLongPress = () => {
    if (longPressTimer.value) {
      clearTimeout(longPressTimer.value);
      longPressTimer.value = null;
    }
    document.removeEventListener("touchend", cancelLongPress);
  };

  document.addEventListener("touchend", cancelLongPress);
};

// 触摸拖拽过程
const onTouchDrag = (event: TouchEvent) => {
  if (!isDragging.value) return;

  // 阻止默认滚动行为
  event.preventDefault();

  const touch = event.touches[0];
  if (!touch) return;

  // 计算新位置
  const newX = touch.clientX - dragOffset.value.x;
  const newY = touch.clientY - dragOffset.value.y;

  // 限制在视窗范围内
  const maxX = window.innerWidth - 250;
  const maxY = window.innerHeight - 100;

  position.value = {
    x: Math.max(0, Math.min(newX, maxX)),
    y: Math.max(0, Math.min(newY, maxY)),
  };
};

// 触摸拖拽结束
const stopTouchDrag = (event: TouchEvent) => {
  const touchDuration = Date.now() - touchStartTime.value;

  // 清除长按定时器
  if (longPressTimer.value) {
    clearTimeout(longPressTimer.value);
    longPressTimer.value = null;
  }

  const wasLongPress = isLongPress.value;
  isDragging.value = false;
  isLongPress.value = false;

  // 移除触摸事件监听
  document.removeEventListener("touchmove", onTouchDrag);
  document.removeEventListener("touchend", stopTouchDrag);

  // 恢复页面滚动
  document.body.style.overflow = "";
  document.body.style.position = "";
  document.body.style.width = "";

  // 如果进行了拖拽，保存位置
  if (wasLongPress) {
    localStorage.setItem(
      "websocketStatusPosition",
      JSON.stringify(position.value)
    );
    return;
  }

  // 如果是短时间触摸且没有长按，视为点击
  if (touchDuration < 300) {
    // 检查是否是双击
    const now = Date.now();
    if (now - lastTouchTime.value < 500) {
      // 双击重置位置
      resetPosition(event);
    } else {
      // 单击展开/收起
      setTimeout(() => {
        toggleExpanded();
      }, 10);
    }
    lastTouchTime.value = now;
  }
};

// 定时更新状态
let statusTimer: NodeJS.Timeout | null = null;

// 窗口大小改变时调整位置
const handleResize = () => {
  const maxX = window.innerWidth - 250;
  const maxY = window.innerHeight - 100;

  // 如果当前位置超出边界，调整到边界内
  if (position.value.x > maxX || position.value.y > maxY) {
    position.value = {
      x: Math.min(position.value.x, maxX),
      y: Math.min(position.value.y, maxY),
    };
    // 保存调整后的位置
    localStorage.setItem(
      "websocketStatusPosition",
      JSON.stringify(position.value)
    );
  }
};

// 检测是否为触摸设备
const detectTouchDevice = () => {
  isTouchDevice.value =
    "ontouchstart" in window ||
    navigator.maxTouchPoints > 0 ||
    (navigator as any).msMaxTouchPoints > 0;
};

onMounted(() => {
  updateStatus();
  restorePosition(); // 恢复保存的位置
  detectTouchDevice(); // 检测设备类型
  statusTimer = setInterval(updateStatus, 2000); // 每2秒更新一次状态

  // 监听窗口大小变化
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  if (statusTimer) {
    clearInterval(statusTimer);
  }

  // 清理长按定时器
  if (longPressTimer.value) {
    clearTimeout(longPressTimer.value);
  }

  // 清理事件监听
  window.removeEventListener("resize", handleResize);
  document.removeEventListener("mousemove", onDrag);
  document.removeEventListener("mouseup", stopDrag);
  document.removeEventListener("touchmove", onTouchDrag);
  document.removeEventListener("touchend", stopTouchDrag);

  // 恢复页面状态
  document.body.style.userSelect = "";
  document.body.style.overflow = "";
  document.body.style.position = "";
  document.body.style.width = "";
});
</script>

<style scoped>
.websocket-status {
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 8px;
  padding: 8px;
  font-size: 12px;
  z-index: 9999;
  min-width: 200px;
  backdrop-filter: blur(10px);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  /* 移动端优化 */
  touch-action: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

.websocket-status:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .websocket-status {
    min-width: 180px;
    font-size: 11px;
    padding: 6px;
  }
}

@media (max-width: 480px) {
  .websocket-status {
    min-width: 160px;
    font-size: 10px;
    padding: 4px;
  }
}

/* 长按拖拽状态 */
.websocket-status.long-press {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5);
  background: rgba(0, 0, 0, 0.9);
}

.status-header {
  display: flex;
  align-items: center;
  cursor: move;
  user-select: none;
  padding: 2px 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  /* 移动端触摸优化 */
  -webkit-tap-highlight-color: transparent;
  touch-action: none;
}

.status-header:hover {
  background: rgba(255, 255, 255, 0.1);
}

.status-header.dragging {
  background: rgba(255, 255, 255, 0.2);
  cursor: grabbing;
}

/* 移动端触摸反馈 */
@media (hover: none) and (pointer: coarse) {
  .status-header {
    cursor: default;
    padding: 4px 6px; /* 增加触摸区域 */
  }

  .status-header:active {
    background: rgba(255, 255, 255, 0.15);
    transform: scale(0.98);
  }
}

.drag-handle {
  color: #999;
  margin-right: 6px;
  font-size: 10px;
  line-height: 1;
  cursor: grab;
  transition: color 0.2s ease;
}

.status-header.dragging .drag-handle {
  cursor: grabbing;
  color: #fff;
}

/* 移动端拖拽手柄优化 */
@media (hover: none) and (pointer: coarse) {
  .drag-handle {
    font-size: 12px;
    color: #bbb;
    margin-right: 8px;
  }

  .status-header:active .drag-handle {
    color: #fff;
  }
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}

.indicator-success {
  background-color: #4caf50;
}

.indicator-warning {
  background-color: #ff9800;
}

.indicator-error {
  background-color: #f44336;
}

.status-text {
  flex: 1;
}

.toggle-icon {
  margin-left: 8px;
  font-size: 10px;
}

.status-details {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.label {
  color: #ccc;
}

.value {
  font-weight: bold;
}

.value.success {
  color: #4caf50;
}

.value.warning {
  color: #ff9800;
}

.value.error {
  color: #f44336;
}

.actions {
  margin-top: 8px;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.status-success {
  border-left: 3px solid #4caf50;
}

.status-warning {
  border-left: 3px solid #ff9800;
}

.status-error {
  border-left: 3px solid #f44336;
}
</style>
