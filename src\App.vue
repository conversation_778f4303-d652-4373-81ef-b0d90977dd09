<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import LoadingSpinner from "./components/LoadingSpinner.vue";
import { toast, showWarning, showSuccess, closeToast } from "@/utils/toast";
import { getToken } from "@/utils/auth";
import { loginEvent, SHOW_LOGIN_EVENT } from "@/api/request";
import LoginDialog from "@/components/LoginDialog.vue";
import CommonDialog from "@/components/CommonDialog.vue";
import { loginPopup, oncePopup } from "@/api/activity";
import { useStore } from "vuex";
import { useRoute, useRouter } from "vue-router";
import websocketService from "@/utils/websocket";
import networkMonitor, { NetworkStatus } from "@/utils/networkMonitor";
import websocketKeeper from "@/utils/websocketKeeper";

import WebSocketStatus from "@/components/WebSocketStatus.vue";

// WebSocket状态切换事件名称
const WEBSOCKET_STATUS_TOGGLE_EVENT = 'websocket-status-toggle';

const isLoading = ref(true);
const showLoginDialog = ref(false);
const store = useStore();
const route = useRoute();
const router = useRouter();

// WebSocket登录状态管理
const websocketLoginSent = ref(false);
const websocketLoginSuccess = ref(false);


// 网络状态响应式引用
const networkStatus = networkMonitor.networkStatus;
const connectionQuality = networkMonitor.connectionQuality;

// 登录活动弹框
const commonDialogShow = ref(false);
const dialogObj = ref();

const getLoginPopup = () => {
  loginPopup().then((res: any) => {
    console.log(res);
    if (res.length) {
      commonDialogShow.value = true;
      dialogObj.value = res[0];
    }
  });
};
const getLoginPopupOnce = () => {
  oncePopup().then((res: any) => {
    console.log(res);
    if (res.length) {
      commonDialogShow.value = true;
      dialogObj.value = res[0];
      localStorage.setItem("dialogOnce", "true");
    }
  });
};

// 最小加载动画显示时间 (毫秒)
const MIN_LOADING_TIME = 200; // 确保 SVG 动画播放完毕 (3.5s + buffer)

// 初始化系统
const initializeSystem = async () => {
  const startTime = Date.now();
  try {
    // 如果有 token，获取用户信息
    if (getToken()) {
      await store.dispatch("auth/fetchUserInfo");
    }
    // 检查 inviteCode
    if (localStorage.getItem("inviteCode") && !getToken()) {
      showLoginDialog.value = true;
    }
    // 这里可以添加其他系统初始化需要的操作
    // await new Promise(resolve => setTimeout(resolve, 1000)) // 移除或调整模拟延迟
  } catch (error) {
    console.error("Initialization failed:", error);
    // Handle initialization error if needed
  } finally {
    const elapsedTime = Date.now() - startTime;
    const remainingTime = MIN_LOADING_TIME - elapsedTime;

    if (remainingTime > 0) {
      // 如果初始化时间小于最小显示时间，则等待剩余时间
      setTimeout(() => {
        isLoading.value = false;
      }, remainingTime);
    } else {
      // 如果初始化时间已经足够长，则立即隐藏加载动画
      isLoading.value = false;
    }
  }
};
// 处理登录成功
const handleLoginSuccess = () => {
  showLoginDialog.value = false;
  getLoginPopup();
};
// 处理登录弹窗事件
const handleShowLogin = () => {
  if (!showLoginDialog.value) {
    showLoginDialog.value = true;
  }
};
// 创建SSE推送链接
const eventSource = ref();
const initSSE = () => {
  if (typeof EventSource !== "undefined") {
    eventSource.value = new EventSource(
      `${window.location.origin}/front/activity-events/jackpot-update`
    );
    eventSource.value.addEventListener(
      "open",
      function (open: any) {
        console.log("SSE已成功打开");
      },
      false
    );
    eventSource.value.addEventListener("message", (event: { data: any }) => {
      // console.log("收到消息内容是:", JSON.parse(event.data));
      const msg = JSON.parse(event.data);
      //  store.dispatch('jackpot/updateJackpotBalance',{amount: 20,time:msg.time});;
      if (msg?.id) {
        switch (msg?.id) {
          case 2:
            // 更新獎池數據
            store.dispatch("jackpot/updateJackpotBalance", msg);
            break; /* 可選的 */
          case 3:
            if (msg.user_id === store.state.auth.user.id) {
              showSuccess(msg.invitee_id + "Ajudando você a ter sucesso");
            }

            break; /* 可選的 */
          /* 您可以有任意数量的 case 语句 */
          default: /* 可选的 */
        }
      }
    });
    eventSource.value.addEventListener(
      "error",
      (error: { currentTarget: any }) => {
        console.log("SSE 连接出错：", error);
        let { currentTarget } = error;
        console.log("SSE 连接出错：", currentTarget);
        if (currentTarget.readyState === 2) {
          setTimeout(() => {
            initSSE();
          }, 360000);
        }
        // eventSource.close();
      }
    );
  }
};

// 初始化WebSocket服务
const initWebSocket = async () => {
  try {
    // 监听token过期事件
    websocketService.onTokenExpired((event) => {
      const { message } = event.detail;
      console.log("Token已过期:", message);

      // 清除store中的用户信息
      store.dispatch("auth/logout", true);

      // 显示提示信息
      showWarning(message);

      // 跳转到首页
      router.push("/");

      // 显示登录弹窗
      showLoginDialog.value = true;
    });
  } catch (error) {
    console.error("WebSocket连接失败:", error);
  }
};
/**
 * 当元素进入视口时预加载资源
 * @param elementSelector 元素选择器
 * @param resourceUrl 资源地址
 * @param options 可选项：type（资源类型），onLoaded（加载完成回调）
 */
function preloadWhenVisible(
  elementSelector: string,
  resourceUrl: string,
  options?: {
    type?: "image" | "script" | "style" | "auto";
    onLoaded?: () => void;
  }
) {
  const { type = "auto", onLoaded } = options || {};
  const observedElements = new Set<Element>();

  const getResourceType = () => {
    if (type !== "auto") return type;
    if (/\.(jpg|jpeg|png|gif|webp|svg)$/i.test(resourceUrl)) return "image";
    if (/\.(js)$/i.test(resourceUrl)) return "script";
    if (/\.(css)$/i.test(resourceUrl)) return "style";
    return "auto";
  };

  const preloadResource = (url: string) => {
    const resourceType = getResourceType();
    let el: HTMLLinkElement | HTMLImageElement | HTMLScriptElement | null =
      null;

    switch (resourceType) {
      case "image":
        el = new Image();
        el.src = url;
        el.onload = () => onLoaded && onLoaded();
        break;
      case "script":
        el = document.createElement("script");
        el.src = url;
        el.async = true;
        el.onload = () => onLoaded && onLoaded();
        document.head.appendChild(el);
        break;
      case "style":
        el = document.createElement("link");
        el.rel = "stylesheet";
        el.href = url;
        el.onload = () => onLoaded && onLoaded();
        document.head.appendChild(el);
        break;
      default:
        el = document.createElement("link");
        el.rel = "preload";
        el.href = url;
        el.as = "fetch";
        el.onload = () => onLoaded && onLoaded();
        document.head.appendChild(el);
    }
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting && !observedElements.has(entry.target)) {
        observedElements.add(entry.target);
        preloadResource(resourceUrl);
        observer.unobserve(entry.target);
      }
    });
  });

  const elements = document.querySelectorAll(elementSelector);
  elements.forEach((el) => observer.observe(el));
}
async function websocketToken() {
  // 获取用户信息
  const userInfo = store.state.auth.user;
  if (!userInfo || !userInfo.uuid) {
    console.log("用户信息不完整，跳过WebSocket登录");
    return;
  }

  try {
    // 等待下一个tick，确保DOM更新完成
    await nextTick();

    // 确保websocket已连接
    await websocketService.connect();

    // 使用优化后的登录方法
    const success = await websocketService.sendLogin();
    if (success) {
      console.log("WebSocket登录消息发送成功");
      // 更新本地状态
      websocketLoginSent.value = true;
      websocketLoginSuccess.value = true;
      localStorage.setItem('isLogin', 'false');
    } else {
      console.log("WebSocket登录消息发送失败或被跳过");
    }
  } catch (error) {
    console.error("发送WebSocket登录消息失败:", error);
  }
}

// 监听token状态变化
watch(
  () => store.state.auth.user?.uuid,
  (user) => {
    console.log("store.state.auth.user?.uuid", user);
    if (user || localStorage.getItem("token")) {
      // 有token时调用websocketToken
      websocketToken();
    } else {
      // 用户登出时重置WebSocket登录状态
      websocketLoginSent.value = false;
      websocketLoginSuccess.value = false;
      websocketService.resetLoginState();
      websocketService.disconnect();
    }
  },
  { immediate: true }
);

// 监听网络状态变化，当网络恢复时重新发送登录消息
watch(
  () => networkStatus.value,
  (newStatus, oldStatus) => {
    console.log(`网络状态变化: ${oldStatus} -> ${newStatus}`);

    // 当网络从离线恢复为在线时，重新发送登录消息
    if (
      oldStatus === NetworkStatus.OFFLINE &&
      newStatus === NetworkStatus.ONLINE
    ) {
      console.log("网络恢复，重新发送WebSocket登录消息");
      showSuccess("网络连接已恢复");

      // 重置登录状态，允许重新发送
      websocketLoginSent.value = false;
      websocketLoginSuccess.value = false;
      websocketService.resetLoginState();

      // 延迟发送登录消息，给网络一些恢复时间
      setTimeout(() => {
        if (store.state.auth.user?.uuid) {
          websocketToken();
        }
      }, 2000);
    } else if (newStatus === NetworkStatus.OFFLINE) {
      showWarning("网络连接已断开");
    } else if (newStatus === NetworkStatus.SLOW) {
      showWarning("网络连接较慢，可能影响实时通信");
    }
  }
);

// 监听连接质量变化
watch(
  () => connectionQuality.value,
  (newQuality, oldQuality) => {
    if (newQuality.status !== oldQuality.status) {
      console.log(
        `连接质量变化: ${oldQuality.status} -> ${newQuality.status}, 延迟: ${newQuality.latency}ms`
      );
    }
  }
);

// 快捷键状态跟踪
const keyState = ref({
  ctrl: false,
  shift: false,
  o: false,
  p: false
});

// 快捷键处理函数
const handleKeyDown = (event: KeyboardEvent) => {
  // 更新按键状态
  if (event.key === 'Control') keyState.value.ctrl = true;
  if (event.key === 'Shift') keyState.value.shift = true;
  if (event.key.toLowerCase() === 'o') keyState.value.o = true;
  if (event.key.toLowerCase() === 'p') keyState.value.p = true;

  // 检查是否按下了完整的快捷键组合 Ctrl+Shift+O+P
  if (keyState.value.ctrl && keyState.value.shift && keyState.value.o && keyState.value.p) {
    event.preventDefault();
    event.stopPropagation();

    // 触发WebSocket状态切换事件
    window.dispatchEvent(new Event(WEBSOCKET_STATUS_TOGGLE_EVENT));

    console.log('快捷键 Ctrl+Shift+O+P 被触发，切换WebSocket状态监听组件');

    // 重置按键状态
    resetKeyState();
  }
};

const handleKeyUp = (event: KeyboardEvent) => {
  // 重置按键状态
  if (event.key === 'Control') keyState.value.ctrl = false;
  if (event.key === 'Shift') keyState.value.shift = false;
  if (event.key.toLowerCase() === 'o') keyState.value.o = false;
  if (event.key.toLowerCase() === 'p') keyState.value.p = false;
};

const resetKeyState = () => {
  keyState.value = {
    ctrl: false,
    shift: false,
    o: false,
    p: false
  };
};

// 统一的初始化逻辑
onMounted(async () => {
  // 预加载任意资源，自动判断类型
  preloadWhenVisible(".some-block", "/api/data.json", {
    onLoaded: () => {
      console.log("资源已预加载");
    },
  });
  initializeSystem();
  initSSE();
  // initWebSocket(); // 初始化WebSocket连接

  // 启动网络监控（这会自动处理网络状态变化和WebSocket重连）
  networkMonitor.startMonitoring();
  // 启动WebSocket连接保持器
  websocketKeeper.start();
  // 监听登录弹窗事件
  loginEvent.addEventListener(SHOW_LOGIN_EVENT, handleShowLogin);
  if (localStorage.getItem("dialogOnce") !== "true") {
    getLoginPopupOnce();
  }

  // 添加快捷键监听
  document.addEventListener('keydown', handleKeyDown);
  document.addEventListener('keyup', handleKeyUp);

  // 监听路由变化，关闭 Toast
  router.beforeEach((to, from, next) => {
    if (to.path !== from.path) {
      closeToast();
    }
    next();
  });
});

// 移除事件监听
onUnmounted(() => {
  loginEvent.removeEventListener(SHOW_LOGIN_EVENT, handleShowLogin);
  // 移除快捷键监听
  document.removeEventListener('keydown', handleKeyDown);
  document.removeEventListener('keyup', handleKeyUp);
  // 停止网络监控
  networkMonitor.stopMonitoring();
  // 停止WebSocket保持器
  websocketKeeper.stop();
  // 组件卸载时清除标记
  const pageKey = "pageOpened_" + route.path;
  sessionStorage.removeItem(pageKey);
  websocketService.disconnect(); // 卸载时关闭WebSocket连接
});
</script>

<template>
  <v-app>
    <!-- 系统加载动画 -->
    <loading-spinner :show="isLoading" text="Carregando..." />

    <router-view v-show="!isLoading" v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>

    <!-- PWA 安装按钮 -->
    <!-- <pwa-install-button /> -->

    <!-- Toast 提示 -->
    <v-snackbar
      v-model="toast.show"
      :color="toast.color"
      :timeout="toast.timeout"
      location="center"
    >
      {{ toast.text }}
    </v-snackbar>

    <!-- 登录弹窗 -->
    <login-dialog v-model="showLoginDialog" @login="handleLoginSuccess" />
    <CommonDialog
      :show="commonDialogShow"
      @update:show="commonDialogShow = $event"
      :dialogObj="dialogObj"
    />

    <!-- WebSocket状态监控 -->
    <WebSocketStatus />
  </v-app>
</template>

<style lang="scss">
/* 引入 Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700&display=swap");

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 全局样式 */
html,
body {
  margin: 0;
  padding: 0;
  font-family: "Nunito", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  min-height: 100vh;
}

.v-field__input {
  -webkit-user-select: text !important;
  user-select: text !important;
}
/* // 解决ios设备input聚焦异常问题。 */
</style>
